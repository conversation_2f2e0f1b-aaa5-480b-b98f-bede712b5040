2025-07-24 22:09:55 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 22:09:55 | INFO | __main__:main:452 | Starting interactive CLI mode
2025-07-24 22:09:55 | INFO | __main__:run_cli_mode:279 | Starting CLI mode
2025-07-24 22:09:59 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 22:10:01 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 22:10:01 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 22:10:01 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 22:10:01 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 22:10:01 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 22:11:38 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 22:11:38 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 22:11:38 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 22:11:38 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 22:11:38 | INFO | src.utils.logging_config:log_start:140 | Starting iterative analysis for topic: hierarchy
2025-07-24 22:11:38 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 22:11:38 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-24 22:11:38 | ERROR | src.utils.logging_config:log_error:153 | Analysis error iterative hierarchy analysis: Model validation failed: ["Model 'llama2' not found"]
2025-07-24 22:15:53 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 22:39:20 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 22:39:23 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 22:39:25 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 22:39:25 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 22:39:25 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 22:39:25 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-24 22:40:21 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 22:40:21 | INFO | __main__:main:450 | Starting GUI mode
2025-07-24 22:40:21 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-24 22:40:23 | ERROR | __main__:main:465 | Application error: name 'DEFAULT_INPUT_FILE' is not defined
2025-07-24 22:41:18 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 22:41:18 | INFO | __main__:main:450 | Starting GUI mode
2025-07-24 22:41:18 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-24 22:41:22 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 22:41:22 | INFO | src.gui.main_gui:__init__:59 | Main GUI initialized
2025-07-24 22:41:22 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 22:41:22 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-24 22:41:22 | INFO | src.gui.main_gui:_update_status:714 | Status: Found 14 models
2025-07-24 22:42:00 | INFO | __main__:main:462 | Application terminated by user
2025-07-24 23:09:10 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 23:09:10 | INFO | __main__:main:457 | Starting interactive CLI mode
2025-07-24 23:09:10 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-24 23:09:14 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:09:16 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:09:16 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 23:09:16 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 23:09:16 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 23:09:16 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 23:10:43 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 23:10:43 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-24 23:11:01 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 23:11:01 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 23:11:01 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 23:11:01 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 23:11:01 | INFO | src.utils.logging_config:log_start:140 | Starting iterative analysis for topic: hierarchy
2025-07-24 23:11:01 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 23:16:06 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 23:16:06 | INFO | __main__:main:457 | Starting interactive CLI mode
2025-07-24 23:16:06 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-24 23:16:09 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:16:11 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:16:11 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 23:16:11 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 23:16:11 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 23:16:11 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 23:35:11 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 23:35:11 | INFO | __main__:main:457 | Starting interactive CLI mode
2025-07-24 23:35:11 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-24 23:35:15 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:35:17 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:35:17 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 23:35:17 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 23:35:17 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 23:35:17 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 23:36:59 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 23:36:59 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 23:36:59 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 23:36:59 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 23:36:59 | INFO | src.utils.logging_config:log_start:140 | Starting iterative analysis for topic: hierarchy
2025-07-24 23:36:59 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 23:36:59 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-24 23:36:59 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 23:37:11 | INFO | src.analysis.iterative:analyze_hierarchy:91 | Starting iteration 1
2025-07-24 23:37:11 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 0 with 1 nodes
2025-07-24 23:37:11 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-24 23:37:42 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 23:37:42 | INFO | __main__:main:457 | Starting interactive CLI mode
2025-07-24 23:37:42 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-24 23:37:45 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:37:47 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:37:47 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 23:37:47 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 23:37:47 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 23:37:47 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 23:44:02 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 23:44:02 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-24 23:44:02 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-24 23:44:06 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:44:08 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:44:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 23:44:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 23:44:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 23:44:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-24 23:45:02 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-24 23:45:02 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-24 23:45:02 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-24 23:45:05 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:45:08 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-24 23:45:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-24 23:45:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-24 23:45:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-24 23:45:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 00:09:11 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 00:09:11 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-25 00:09:11 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-25 00:09:14 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 00:09:16 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 00:09:16 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 00:09:16 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 00:09:16 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 00:09:16 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 00:10:03 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 00:10:03 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-25 00:10:03 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-25 00:10:06 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 00:10:08 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 00:10:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 00:10:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 00:10:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 00:10:08 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 00:10:29 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 00:13:03 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:13:03 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 00:23:36 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 00:23:36 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 00:23:36 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 00:23:36 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 00:23:36 | INFO | src.utils.logging_config:log_start:140 | Starting iterative analysis for topic: hierarchy
2025-07-25 00:23:36 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:23:36 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 00:23:36 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:23:41 | INFO | src.analysis.iterative:analyze_hierarchy:91 | Starting iteration 1
2025-07-25 00:23:41 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 0 with 3 nodes
2025-07-25 00:23:41 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:24:01 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:24:18 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:24:32 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 1 with 2 nodes
2025-07-25 00:24:34 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:24:53 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:25:10 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 2 with 2 nodes
2025-07-25 00:25:12 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:25:31 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:25:43 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 3 with 2 nodes
2025-07-25 00:25:45 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:26:07 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:26:07 | ERROR | src.utils.logging_config:log_error:153 | Analysis error iterative node a9bb48fb-77e5-4f41-b8a9-58b81c436d54: Ollama API error: Simple generation failed: Model 'llama3:latest' not found. Available models: ['llama3:latest', 'gemma3:1b', 'llama3.2:latest', 'smollm2:135m', 'smollm:135m', 'qwen3:0.6b', 'tinyllama:latest', 'wizard-math:latest', 'cogito:latest', 'gemma3:4b', 'llama3.2-vision:latest', 'tinyllama:1.1b', 'gemma3:27b', 'deepseek-r1:8b']
2025-07-25 00:26:07 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 4 with 1 nodes
2025-07-25 00:26:07 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:26:25 | INFO | src.analysis.iterative:analyze_hierarchy:91 | Starting iteration 2
2025-07-25 00:26:25 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 0 with 3 nodes
2025-07-25 00:26:27 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:26:50 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:27:09 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:27:25 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 1 with 2 nodes
2025-07-25 00:27:27 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 00:27:40 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 00:27:40 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-25 00:27:40 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-25 00:27:43 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 00:27:45 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 00:27:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 00:27:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 00:27:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 00:27:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 08:44:54 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 08:44:54 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-25 08:44:54 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-25 08:44:57 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 08:44:59 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 08:44:59 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 08:44:59 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 08:44:59 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 08:44:59 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 08:46:04 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 08:46:04 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-25 08:46:04 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-25 08:46:07 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 08:46:09 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 08:46:09 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 08:46:09 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 08:46:09 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 08:46:09 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 08:47:36 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 08:47:36 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 08:47:36 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 08:47:36 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 08:47:36 | INFO | src.utils.logging_config:log_start:140 | Starting iterative analysis for topic: hierarchy
2025-07-25 08:47:36 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:47:36 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 08:47:36 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:47:41 | INFO | src.analysis.iterative:analyze_hierarchy:91 | Starting iteration 1
2025-07-25 08:47:41 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 0 with 1 nodes
2025-07-25 08:47:41 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:48:00 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 1 with 1 nodes
2025-07-25 08:48:02 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:48:20 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 2 with 1 nodes
2025-07-25 08:48:22 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:48:40 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 3 with 1 nodes
2025-07-25 08:48:42 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:49:01 | INFO | src.analysis.iterative:analyze_hierarchy:91 | Starting iteration 2
2025-07-25 08:49:01 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 0 with 1 nodes
2025-07-25 08:49:03 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:49:25 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 1 with 1 nodes
2025-07-25 08:49:27 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:49:49 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 2 with 1 nodes
2025-07-25 08:49:51 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:50:10 | INFO | src.analysis.iterative:_perform_iteration:168 | Processing level 3 with 1 nodes
2025-07-25 08:50:12 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 08:50:34 | INFO | src.analysis.iterative:analyze_hierarchy:103 | Convergence achieved at iteration 2
2025-07-25 08:50:34 | INFO | src.utils.logging_config:log_completion:149 | Completed iterative analysis for topic: hierarchy in 178.39s
2025-07-25 08:53:51 | ERROR | src.cli.interactive:_handle_error:523 | CLI error: 'CLIInterface' object has no attribute '_view_detailed_results'
2025-07-25 09:04:37 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:04:37 | INFO | src.utils.eta:start_task:117 | Started tracking task test_task (test) with 100 items
2025-07-25 09:04:38 | INFO | src.utils.eta:complete_task:177 | Completed task test_task in 1.01s
2025-07-25 09:04:47 | INFO | src.gui.enhanced_status:start_operation:133 | Started operation: Test Analysis with 50 items
2025-07-25 09:04:52 | INFO | src.gui.enhanced_status:complete_operation:173 | Operation completed: Test Analysis, success: True
2025-07-25 09:05:04 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:11:08 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:11:23 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:11:35 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:30:59 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:30:59 | INFO | __main__:main:454 | Starting GUI mode
2025-07-25 09:30:59 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-25 09:31:03 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 09:31:05 | INFO | src.gui.main_gui:__init__:61 | Main GUI initialized
2025-07-25 09:31:05 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 09:31:05 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 09:31:05 | INFO | src.gui.main_gui:_update_status:897 | Status: Found 14 models
2025-07-25 09:31:44 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:31:44 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-25 09:31:44 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-25 09:31:47 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 09:31:49 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 09:31:49 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 09:31:49 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 09:31:49 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 09:31:49 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 09:37:24 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:38:22 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:39:03 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:39:03 | INFO | src.utils.file_io:write_json:226 | Successfully wrote JSON to C:\Users\<USER>\AppData\Local\Temp\tmpi80tcbj7\test_export.json
2025-07-25 09:39:03 | INFO | src.utils.file_io:read_json:196 | Successfully read JSON from C:\Users\<USER>\AppData\Local\Temp\tmpi80tcbj7\test_export.json
2025-07-25 09:39:57 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:39:57 | INFO | __main__:main:454 | Starting GUI mode
2025-07-25 09:39:57 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-25 09:40:00 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 09:40:01 | INFO | src.gui.main_gui:__init__:61 | Main GUI initialized
2025-07-25 09:40:01 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 09:40:01 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 09:40:01 | INFO | src.gui.main_gui:_update_status:1003 | Status: Found 14 models
2025-07-25 09:41:12 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:41:12 | INFO | src.utils.file_io:write_json:226 | Successfully wrote JSON to C:\Users\<USER>\AppData\Local\Temp\tmp5tlr_mtj\test_export.json
2025-07-25 09:41:12 | INFO | src.utils.file_io:read_json:196 | Successfully read JSON from C:\Users\<USER>\AppData\Local\Temp\tmp5tlr_mtj\test_export.json
2025-07-25 09:42:02 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 09:42:02 | INFO | src.utils.file_io:write_json:226 | Successfully wrote JSON to F:\Deep Research Tool\Deep_Research_Tool_V1\Main-Deep_Research_Tool_V1\output_results\sample_analysis_results_20250725_094202.json
2025-07-25 10:30:37 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 10:30:37 | INFO | __main__:main:454 | Starting GUI mode
2025-07-25 10:30:37 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-25 10:30:40 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 10:30:41 | INFO | src.gui.main_gui:__init__:61 | Main GUI initialized
2025-07-25 10:30:41 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 10:30:41 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 10:30:41 | INFO | src.gui.main_gui:_update_status:1003 | Status: Found 14 models
2025-07-25 10:30:53 | INFO | src.gui.hierarchy_explorer:load_hierarchy:89 | Loading hierarchy: Money
2025-07-25 10:30:53 | INFO | src.gui.hierarchy_explorer:load_hierarchy:104 | Loaded 0 topics
2025-07-25 10:30:53 | INFO | src.gui.main_gui:_update_status:1003 | Status: New hierarchy 'Money' created
2025-07-25 10:31:27 | INFO | src.gui.hierarchy_explorer:load_hierarchy:89 | Loading hierarchy: Money
2025-07-25 10:31:39 | ERROR | src.gui.main_gui:_add_topic:700 | Add topic error: Invalid column index node_id
2025-07-25 10:31:55 | INFO | __main__:main:466 | Application terminated by user
2025-07-25 10:50:33 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 10:50:33 | INFO | __main__:main:461 | Starting interactive CLI mode
2025-07-25 10:50:33 | INFO | __main__:run_cli_mode:284 | Starting CLI mode
2025-07-25 10:50:37 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 10:50:39 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 10:50:39 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 10:50:39 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 10:50:39 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 10:50:39 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 10:50:55 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 10:50:55 | INFO | __main__:main:454 | Starting GUI mode
2025-07-25 10:50:55 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-25 10:50:59 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 10:50:59 | INFO | src.gui.main_gui:__init__:61 | Main GUI initialized
2025-07-25 10:50:59 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 10:50:59 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 10:50:59 | INFO | src.gui.main_gui:_update_status:1003 | Status: Found 14 models
2025-07-25 10:51:24 | INFO | src.gui.hierarchy_explorer:load_hierarchy:97 | Loading hierarchy: Money
2025-07-25 10:51:24 | INFO | src.gui.hierarchy_explorer:load_hierarchy:112 | Loaded 0 topics
2025-07-25 10:51:24 | INFO | src.gui.main_gui:_update_status:1003 | Status: New hierarchy 'Money' created
2025-07-25 10:51:33 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 10:51:33 | INFO | src.gui.hierarchy_explorer:load_hierarchy:97 | Loading hierarchy: Test Hierarchy
2025-07-25 10:51:33 | INFO | src.gui.hierarchy_explorer:load_hierarchy:112 | Loaded 1 topics
2025-07-25 10:51:33 | INFO | src.gui.hierarchy_explorer:load_hierarchy:97 | Loading hierarchy: Money
2025-07-25 10:51:33 | INFO | src.gui.hierarchy_explorer:load_hierarchy:112 | Loaded 1 topics
2025-07-25 10:51:49 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 10:58:37 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 10:58:37 | INFO | __main__:main:454 | Starting GUI mode
2025-07-25 10:58:37 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-25 10:58:40 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 10:58:41 | INFO | src.gui.main_gui:__init__:61 | Main GUI initialized
2025-07-25 10:58:41 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 10:58:41 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 10:58:41 | INFO | src.gui.main_gui:_update_status:1011 | Status: Found 14 models
2025-07-25 11:04:25 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 11:04:32 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 11:04:41 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 11:04:43 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 11:04:45 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 11:04:45 | INFO | src.utils.logging_config:log_start:140 | Starting recursive analysis for topic: hierarchy
2025-07-25 11:04:45 | INFO | src.utils.eta:start_task:117 | Started tracking task recursive_analysis_1753466685 (recursive) with 8 items
2025-07-25 11:04:45 | INFO | src.utils.logging_config:log_completion:149 | Completed recursive analysis for topic: hierarchy in 0.00s
2025-07-25 11:04:45 | INFO | src.utils.eta:complete_task:177 | Completed task recursive_analysis_1753466685 in 0.00s
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 11:04:45 | INFO | src.analysis.recursive:cancel_analysis:54 | Analysis cancellation requested
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 11:04:45 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 11:04:45 | INFO | src.utils.logging_config:log_start:140 | Starting recursive analysis for topic: hierarchy
2025-07-25 11:04:45 | INFO | src.utils.eta:start_task:117 | Started tracking task recursive_analysis_1753466685 (recursive) with 2 items
2025-07-25 11:04:45 | INFO | src.utils.logging_config:log_completion:149 | Completed recursive analysis for topic: hierarchy in 0.00s
2025-07-25 11:04:45 | INFO | src.utils.eta:complete_task:177 | Completed task recursive_analysis_1753466685 in 0.00s
2025-07-25 13:02:03 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 13:02:03 | INFO | __main__:main:454 | Starting GUI mode
2025-07-25 13:02:03 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-25 13:02:06 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 13:02:07 | INFO | src.gui.main_gui:__init__:61 | Main GUI initialized
2025-07-25 13:02:07 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:02:07 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 13:02:07 | INFO | src.gui.main_gui:_update_status:1011 | Status: Found 14 models
2025-07-25 13:02:25 | INFO | src.gui.hierarchy_explorer:load_hierarchy:97 | Loading hierarchy: Money
2025-07-25 13:02:25 | INFO | src.gui.hierarchy_explorer:load_hierarchy:112 | Loaded 0 topics
2025-07-25 13:02:25 | INFO | src.gui.main_gui:_update_status:1011 | Status: New hierarchy 'Money' created
2025-07-25 13:02:47 | INFO | src.gui.hierarchy_explorer:load_hierarchy:97 | Loading hierarchy: Money
2025-07-25 13:02:47 | INFO | src.gui.hierarchy_explorer:load_hierarchy:112 | Loaded 1 topics
2025-07-25 13:02:47 | INFO | src.gui.main_gui:_update_status:1011 | Status: Added topic 'Money' with 4 sub-angles
2025-07-25 13:03:29 | INFO | src.gui.enhanced_status:start_operation:133 | Started operation: Iterative Analysis with 15 items
2025-07-25 13:03:31 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 13:03:31 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 13:03:31 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 13:03:31 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 13:03:31 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 13:03:31 | INFO | src.utils.logging_config:log_start:140 | Starting iterative analysis for topic: hierarchy
2025-07-25 13:03:31 | INFO | src.utils.eta:start_task:117 | Started tracking task iterative_analysis_1753473811 (iterative) with 15 items
2025-07-25 13:03:31 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:03:47 | INFO | src.analysis.iterative:analyze_hierarchy:108 | Starting iteration 1
2025-07-25 13:03:47 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 0 with 1 nodes
2025-07-25 13:03:47 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:04:01 | ERROR | src.utils.logging_config:log_error:153 | Analysis error iterative hierarchy analysis: name 'response_time' is not defined
2025-07-25 13:04:01 | ERROR | src.gui.main_gui:_run_analysis_background:874 | Analysis error: name 'response_time' is not defined
2025-07-25 13:04:01 | INFO | src.gui.main_gui:_update_status:1011 | Status: Analysis failed
2025-07-25 13:04:01 | INFO | src.gui.progress_dialog:set_complete:302 | Operation complete: Analysis failed: name 'response_time' is not defined
2025-07-25 13:04:01 | INFO | src.gui.enhanced_status:complete_operation:173 | Operation completed: Iterative Analysis, success: False
2025-07-25 13:04:20 | INFO | src.gui.main_gui:_update_status:1011 | Status: Starting iterative analysis...
2025-07-25 13:14:37 | INFO | src.gui.main_gui:_update_status:1011 | Status: Shutting down...
2025-07-25 13:14:37 | INFO | src.gui.main_gui:_perform_cleanup_and_exit:557 | GUI application shutting down
2025-07-25 13:15:15 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 13:15:21 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 13:16:35 | INFO | src.utils.logging_config:setup_logging:87 | Logging system initialized
2025-07-25 13:16:35 | INFO | __main__:main:454 | Starting GUI mode
2025-07-25 13:16:35 | INFO | __main__:run_gui_mode:295 | Starting GUI mode
2025-07-25 13:16:39 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 13:16:40 | INFO | src.gui.main_gui:__init__:61 | Main GUI initialized
2025-07-25 13:16:40 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:16:40 | INFO | src.ollama.model_manager:refresh_model_list:182 | Refreshed model list: 14 models available
2025-07-25 13:16:40 | INFO | src.gui.main_gui:_update_status:1011 | Status: Found 14 models
2025-07-25 13:16:56 | INFO | src.gui.hierarchy_explorer:load_hierarchy:97 | Loading hierarchy: Money
2025-07-25 13:16:56 | INFO | src.gui.hierarchy_explorer:load_hierarchy:112 | Loaded 0 topics
2025-07-25 13:16:56 | INFO | src.gui.main_gui:_update_status:1011 | Status: New hierarchy 'Money' created
2025-07-25 13:17:19 | INFO | src.gui.hierarchy_explorer:load_hierarchy:97 | Loading hierarchy: Money
2025-07-25 13:17:19 | INFO | src.gui.hierarchy_explorer:load_hierarchy:112 | Loaded 1 topics
2025-07-25 13:17:19 | INFO | src.gui.main_gui:_update_status:1011 | Status: Added topic 'Money' with 4 sub-angles
2025-07-25 13:17:46 | INFO | src.gui.enhanced_status:start_operation:133 | Started operation: Iterative Analysis with 15 items
2025-07-25 13:17:48 | INFO | src.ollama.api:_test_connection:93 | Successfully connected to Ollama at http://localhost:11434/
2025-07-25 13:17:48 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Analysis Introduction' with 2 variables
2025-07-25 13:17:48 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Recursive Analysis' with 3 variables
2025-07-25 13:17:48 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Iterative Refinement' with 2 variables
2025-07-25 13:17:48 | INFO | src.prompting.prompt_editor:create_template:215 | Created template 'Summary Generation' with 1 variables
2025-07-25 13:17:48 | INFO | src.utils.logging_config:log_start:140 | Starting iterative analysis for topic: hierarchy
2025-07-25 13:17:48 | INFO | src.utils.eta:start_task:117 | Started tracking task iterative_analysis_1753474668 (iterative) with 15 items
2025-07-25 13:17:48 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:17:53 | INFO | src.analysis.iterative:analyze_hierarchy:108 | Starting iteration 1
2025-07-25 13:17:53 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 0 with 1 nodes
2025-07-25 13:17:53 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:18:12 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 1 with 1 nodes
2025-07-25 13:18:12 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 1, Level 0: Money... | ETA: 7m 31s | 0.0 nodes/sec
2025-07-25 13:18:14 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:18:30 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 2 with 1 nodes
2025-07-25 13:18:30 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 1, Level 1: Women... | ETA: 6m 4s | 0.0 nodes/sec
2025-07-25 13:18:32 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:18:49 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 3 with 1 nodes
2025-07-25 13:18:49 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 1, Level 2: Men... | ETA: 4m 55s | 0.1 nodes/sec
2025-07-25 13:18:51 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:19:11 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 4 with 1 nodes
2025-07-25 13:19:11 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 1, Level 3: Dating... | ETA: 4m 41s | 0.1 nodes/sec
2025-07-25 13:19:13 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:19:33 | INFO | src.analysis.iterative:analyze_hierarchy:108 | Starting iteration 2
2025-07-25 13:19:33 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 0 with 1 nodes
2025-07-25 13:19:33 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 1, Level 4: Marriage... | ETA: 4m 22s | 0.1 nodes/sec
2025-07-25 13:19:35 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:19:57 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 1 with 1 nodes
2025-07-25 13:19:57 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 2, Level 0: Money... | ETA: 4m 4s | 0.0 nodes/sec
2025-07-25 13:19:59 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:20:18 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 2 with 1 nodes
2025-07-25 13:20:18 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 2, Level 1: Women... | ETA: 3m 38s | 0.0 nodes/sec
2025-07-25 13:20:20 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:20:38 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 3 with 1 nodes
2025-07-25 13:20:38 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 2, Level 2: Men... | ETA: 3m 10s | 0.0 nodes/sec
2025-07-25 13:20:40 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:21:01 | INFO | src.analysis.iterative:_perform_iteration:189 | Processing level 4 with 1 nodes
2025-07-25 13:21:01 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 2, Level 3: Dating... | ETA: 2m 45s | 0.0 nodes/sec
2025-07-25 13:21:03 | INFO | src.ollama.api:list_models:175 | Found 14 available models
2025-07-25 13:21:25 | INFO | src.analysis.iterative:analyze_hierarchy:120 | Convergence achieved at iteration 2
2025-07-25 13:21:25 | INFO | src.utils.logging_config:log_completion:149 | Completed iterative analysis for topic: hierarchy in 217.64s
2025-07-25 13:21:25 | INFO | src.utils.eta:complete_task:177 | Completed task iterative_analysis_1753474668 in 217.64s
2025-07-25 13:21:25 | INFO | src.gui.main_gui:_update_status:1011 | Status: Iteration 2, Level 4: Marriage... | ETA: 2m 20s | 0.0 nodes/sec
2025-07-25 13:21:25 | INFO | src.gui.main_gui:_update_status:1011 | Status: Analysis completed
2025-07-25 13:21:25 | INFO | src.gui.progress_dialog:set_complete:302 | Operation complete: Analysis completed with 5 results
2025-07-25 13:21:25 | INFO | src.gui.enhanced_status:complete_operation:173 | Operation completed: Iterative Analysis, success: True
2025-07-25 13:52:59 | INFO | src.gui.main_gui:_update_status:1011 | Status: Starting iterative analysis...
2025-07-25 13:53:02 | INFO | src.gui.main_gui:_update_status:1011 | Status: Shutting down...
2025-07-25 13:53:02 | INFO | src.gui.main_gui:_perform_cleanup_and_exit:557 | GUI application shutting down
